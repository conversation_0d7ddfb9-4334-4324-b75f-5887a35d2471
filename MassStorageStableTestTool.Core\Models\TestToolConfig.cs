using MassStorageStableTestTool.Core.Enums;
using System.Text.Json;

namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 测试工具配置模型
/// </summary>
public class TestToolConfig
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工具类型
    /// </summary>
    public TestToolType Type { get; set; }

    /// <summary>
    /// 可执行文件路径
    /// </summary>
    public string ExecutablePath { get; set; } = string.Empty;

    /// <summary>
    /// 窗口标题（GUI工具使用）
    /// </summary>
    public string? WindowTitle { get; set; }

    /// <summary>
    /// 默认参数
    /// </summary>
    public Dictionary<string, object> DefaultParameters { get; set; } = new();

    /// <summary>
    /// 超时设置
    /// </summary>
    public TimeoutSettings Timeouts { get; set; } = new();

    /// <summary>
    /// 工具描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 工具版本
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 优先级（数字越小优先级越高）
    /// </summary>
    public int Priority { get; set; } = 100;

    /// <summary>
    /// 支持的文件系统类型
    /// </summary>
    public List<string> SupportedFileSystems { get; set; } = new();

    /// <summary>
    /// 最小磁盘空间要求（MB）
    /// </summary>
    public long MinimumDiskSpace { get; set; } = 100;

    /// <summary>
    /// 工具特定的UI配置（GUI工具使用）
    /// </summary>
    public Dictionary<string, object> UIConfig { get; set; } = new();

    /// <summary>
    /// 命令行模板（CLI工具使用）
    /// </summary>
    public string? CommandTemplate { get; set; }

    /// <summary>
    /// 输出解析配置
    /// </summary>
    public OutputParsingConfig OutputParsing { get; set; } = new();

    /// <summary>
    /// 验证工具是否可用
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Name))
        {
            errors.Add("工具名称不能为空");
        }

        if (string.IsNullOrWhiteSpace(ExecutablePath))
        {
            errors.Add("可执行文件路径不能为空");
        }
        else if (!File.Exists(ExecutablePath))
        {
            errors.Add($"可执行文件不存在: {ExecutablePath}");
        }

        if (Type == TestToolType.GUI && string.IsNullOrWhiteSpace(WindowTitle))
        {
            errors.Add("GUI工具必须指定窗口标题");
        }

        if (Type == TestToolType.CLI && string.IsNullOrWhiteSpace(CommandTemplate))
        {
            errors.Add("CLI工具必须指定命令模板");
        }

        if (Timeouts.TestTimeout <= TimeSpan.Zero)
        {
            errors.Add("测试超时时间必须大于0");
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="key">参数键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>参数值</returns>
    public T GetParameter<T>(string key, T defaultValue = default!)
    {
        if (DefaultParameters.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// 设置参数值
    /// </summary>
    /// <param name="key">参数键</param>
    /// <param name="value">参数值</param>
    public void SetParameter(string key, object value)
    {
        DefaultParameters[key] = value;
    }

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public TestToolConfig Clone()
    {
        return new TestToolConfig
        {
            Name = Name,
            Type = Type,
            ExecutablePath = ExecutablePath,
            WindowTitle = WindowTitle,
            DefaultParameters = new Dictionary<string, object>(DefaultParameters),
            Timeouts = Timeouts.Clone(),
            Description = Description,
            Version = Version,
            Enabled = Enabled,
            Priority = Priority,
            SupportedFileSystems = new List<string>(SupportedFileSystems),
            MinimumDiskSpace = MinimumDiskSpace,
            UIConfig = new Dictionary<string, object>(UIConfig),
            CommandTemplate = CommandTemplate,
            OutputParsing = OutputParsing.Clone()
        };
    }
}

/// <summary>
/// 超时设置
/// </summary>
public class TimeoutSettings
{
    /// <summary>
    /// 启动超时时间
    /// </summary>
    public TimeSpan LaunchTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// 测试超时时间
    /// </summary>
    public TimeSpan TestTimeout { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// 关闭超时时间
    /// </summary>
    public TimeSpan ShutdownTimeout { get; set; } = TimeSpan.FromSeconds(2);

    /// <summary>
    /// UI元素查找超时时间
    /// </summary>
    public TimeSpan ElementTimeout { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// 清理超时时间
    /// </summary>
    public TimeSpan CleanupTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// 克隆超时设置
    /// </summary>
    /// <returns>超时设置副本</returns>
    public TimeoutSettings Clone()
    {
        return new TimeoutSettings
        {
            LaunchTimeout = LaunchTimeout,
            TestTimeout = TestTimeout,
            ShutdownTimeout = ShutdownTimeout,
            ElementTimeout = ElementTimeout,
            CleanupTimeout = CleanupTimeout
        };
    }
}

/// <summary>
/// 输出解析配置
/// </summary>
public class OutputParsingConfig
{
    /// <summary>
    /// 输出格式（如 "json", "text", "xml" 等）
    /// </summary>
    public string Format { get; set; } = "text";

    /// <summary>
    /// 解析规则（正则表达式或XPath等）
    /// </summary>
    public Dictionary<string, string> ParsingRules { get; set; } = new();

    /// <summary>
    /// 编码格式
    /// </summary>
    public string Encoding { get; set; } = "UTF-8";

    /// <summary>
    /// 是否忽略大小写
    /// </summary>
    public bool IgnoreCase { get; set; } = true;

    /// <summary>
    /// 克隆解析配置
    /// </summary>
    /// <returns>解析配置副本</returns>
    public OutputParsingConfig Clone()
    {
        return new OutputParsingConfig
        {
            Format = Format,
            ParsingRules = new Dictionary<string, string>(ParsingRules),
            Encoding = Encoding,
            IgnoreCase = IgnoreCase
        };
    }
}
