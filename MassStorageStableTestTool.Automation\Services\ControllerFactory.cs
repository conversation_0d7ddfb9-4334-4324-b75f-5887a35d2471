using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Common;
using MassStorageStableTestTool.Automation.Controllers;
using MassStorageStableTestTool.Automation.GUI;
using System.IO;

namespace MassStorageStableTestTool.Automation.Services;

/// <summary>
/// 控制器工厂接口
/// </summary>
public interface IControllerFactory
{
    /// <summary>
    /// 创建工具控制器
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>工具控制器</returns>
    Task<ITestToolController?> CreateControllerAsync(string toolName);

    /// <summary>
    /// 获取可用的工具名称列表
    /// </summary>
    /// <returns>工具名称列表</returns>
    List<string> GetAvailableToolNames();

    /// <summary>
    /// 注册工具控制器类型
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="controllerType">控制器类型</param>
    void RegisterController(string toolName, Type controllerType);
}

/// <summary>
/// 控制器工厂实现
/// </summary>
public class ControllerFactory : IControllerFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfigurationService _configurationService;
    private readonly ILogger<ControllerFactory> _logger;
    private readonly Dictionary<string, Type> _controllerTypes;

    public ControllerFactory(
        IServiceProvider serviceProvider,
        IConfigurationService configurationService,
        ILogger<ControllerFactory> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _controllerTypes = new Dictionary<string, Type>();

        RegisterDefaultControllers();
    }

    public async Task<ITestToolController?> CreateControllerAsync(string toolName)
    {
        if (string.IsNullOrWhiteSpace(toolName))
        {
            _logger.LogWarning("工具名称不能为空");
            return null;
        }

        if (!_controllerTypes.TryGetValue(toolName, out var controllerType))
        {
            _logger.LogWarning("未找到工具控制器类型: {ToolName}", toolName);
            return null;
        }

        try
        {
            // 获取工具配置
            var toolConfig = await _configurationService.GetToolConfigurationAsync(toolName).ConfigureAwait(false);
            if (toolConfig == null)
            {
                _logger.LogWarning("未找到工具配置: {ToolName}", toolName);
                return null;
            }

            // 从服务提供者获取所需的依赖项
            var automationHelper = _serviceProvider.GetRequiredService<AutomationHelper>();

            // 动态创建正确类型的 ILogger
            var loggerType = typeof(ILogger<>).MakeGenericType(controllerType);
            var logger = _serviceProvider.GetRequiredService(loggerType);

            // 创建控制器实例
            var controller = (ITestToolController?)Activator.CreateInstance(controllerType, toolConfig, automationHelper, logger);

            if (controller == null)
            {
                _logger.LogError("创建控制器实例失败: {ToolName}", toolName);
                return null;
            }

            // 设置服务提供者（如果控制器支持）
            if (controller is BaseTestToolController baseController)
            {
                baseController.SetServiceProvider(_serviceProvider);
            }

            _logger.LogDebug("成功创建控制器: {ToolName}", toolName);
            return controller;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建控制器失败: {ToolName}", toolName);
            return null;
        }
    }

    public List<string> GetAvailableToolNames()
    {
        return _controllerTypes.Keys.ToList();
    }

    public void RegisterController(string toolName, Type controllerType)
    {
        if (string.IsNullOrWhiteSpace(toolName))
        {
            throw new ArgumentException("工具名称不能为空", nameof(toolName));
        }

        if (controllerType == null)
        {
            throw new ArgumentNullException(nameof(controllerType));
        }

        if (!typeof(ITestToolController).IsAssignableFrom(controllerType))
        {
            throw new ArgumentException($"控制器类型必须实现 {nameof(ITestToolController)} 接口", nameof(controllerType));
        }

        _controllerTypes[toolName] = controllerType;
        _logger.LogDebug("注册控制器: {ToolName} -> {ControllerType}", toolName, controllerType.Name);
    }

    private void RegisterDefaultControllers()
    {
        _logger.LogDebug("注册默认控制器");

        // 注册GUI工具控制器
        RegisterController("H2testw", typeof(H2TestController));
        RegisterController("CrystalDiskMark", typeof(CrystalDiskMarkController));
        // 可以继续添加其他控制器
        // RegisterController("ATTO", typeof(AttoController));

        _logger.LogInformation("已注册 {Count} 个默认控制器", _controllerTypes.Count);
    }
}

/// <summary>
/// 工具发现服务接口
/// </summary>
public interface IToolDiscoveryService
{
    /// <summary>
    /// 发现系统中可用的测试工具
    /// </summary>
    /// <returns>可用工具列表</returns>
    Task<List<TestToolConfig>> DiscoverAvailableToolsAsync();

    /// <summary>
    /// 验证工具是否可用
    /// </summary>
    /// <param name="toolConfig">工具配置</param>
    /// <returns>是否可用</returns>
    Task<bool> ValidateToolAsync(TestToolConfig toolConfig);

    /// <summary>
    /// 获取工具版本信息
    /// </summary>
    /// <param name="toolConfig">工具配置</param>
    /// <returns>版本信息</returns>
    Task<string> GetToolVersionAsync(TestToolConfig toolConfig);
}

/// <summary>
/// 工具发现服务实现
/// </summary>
public class ToolDiscoveryService : IToolDiscoveryService
{
    private readonly ILogger<ToolDiscoveryService> _logger;
    private readonly List<string> _commonToolPaths;

    public ToolDiscoveryService(ILogger<ToolDiscoveryService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _commonToolPaths = new List<string>
        {
            @".\third part tools",
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
            Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
            Environment.CurrentDirectory
        };
    }

    public async Task<List<TestToolConfig>> DiscoverAvailableToolsAsync()
    {
        _logger.LogInformation("开始发现可用的测试工具");
        
        var availableTools = new List<TestToolConfig>();
        var toolDefinitions = GetKnownToolDefinitions();

        foreach (var toolDef in toolDefinitions)
        {
            try
            {
                var foundPath = await FindToolExecutableAsync(toolDef.ExecutableNames);
                if (!string.IsNullOrEmpty(foundPath))
                {
                    var toolConfig = new TestToolConfig
                    {
                        Name = toolDef.Name,
                        ExecutablePath = foundPath,
                        WindowTitle = toolDef.WindowTitle,
                        Type = toolDef.ToolType,
                        Enabled = true
                    };

                    if (await ValidateToolAsync(toolConfig))
                    {
                        availableTools.Add(toolConfig);
                        _logger.LogInformation("发现可用工具: {ToolName} at {Path}", toolDef.Name, foundPath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查工具时出错: {ToolName}", toolDef.Name);
            }
        }

        _logger.LogInformation("发现 {Count} 个可用的测试工具", availableTools.Count);
        return availableTools;
    }

    public async Task<bool> ValidateToolAsync(TestToolConfig toolConfig)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(toolConfig.ExecutablePath))
                return false;

            if (!File.Exists(toolConfig.ExecutablePath))
                return false;

            // 可以添加更多验证逻辑，比如检查文件签名、版本等
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "验证工具失败: {ToolName}", toolConfig.Name);
            return false;
        }
    }

    public async Task<string> GetToolVersionAsync(TestToolConfig toolConfig)
    {
        try
        {
            if (!await ValidateToolAsync(toolConfig))
                return "Unknown";

            var fileInfo = new FileInfo(toolConfig.ExecutablePath);
            var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(toolConfig.ExecutablePath);
            
            return versionInfo.FileVersion ?? versionInfo.ProductVersion ?? "Unknown";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取工具版本失败: {ToolName}", toolConfig.Name);
            return "Unknown";
        }
    }

    private async Task<string?> FindToolExecutableAsync(List<string> executableNames)
    {
        foreach (var executableName in executableNames)
        {
            foreach (var basePath in _commonToolPaths)
            {
                if (!Directory.Exists(basePath))
                    continue;

                try
                {
                    var files = Directory.GetFiles(basePath, executableName, SearchOption.AllDirectories);
                    if (files.Any())
                    {
                        return files.First();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "搜索路径时出错: {Path}", basePath);
                }
            }
        }

        return null;
    }

    private List<ToolDefinition> GetKnownToolDefinitions()
    {
        return new List<ToolDefinition>
        {
            new ToolDefinition
            {
                Name = "H2testw",        
                ExecutableNames = new List<string> { "h2testw.exe", "H2testw.exe" },
                WindowTitle = "H2testw",
                ToolType = Core.Enums.TestToolType.GUI
            },
            new ToolDefinition
            {
                Name = "CrystalDiskMark",
                ExecutableNames = new List<string> { "DiskMark64.exe", "DiskMark32.exe", "CrystalDiskMark.exe" },
                WindowTitle = "CrystalDiskMark",
                ToolType = Core.Enums.TestToolType.GUI
            }
        };
    }

    private class ToolDefinition
    {
        public string Name { get; set; } = string.Empty;
        // public string DisplayName { get; set; } = string.Empty;
        public List<string> ExecutableNames { get; set; } = new();
        public string WindowTitle { get; set; } = string.Empty;
        public Core.Enums.TestToolType ToolType { get; set; }
    }
}
